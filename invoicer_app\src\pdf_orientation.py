"""
PDF Orientation Detection and Correction Module for MacInvoicer

This module provides functionality to detect and correct PDF orientation issues
during the import/upload stage. It uses OCR-based text analysis to determine
if a PDF is rotated and automatically corrects the orientation for optimal
readability in the viewer.

Key Features:
    - Automatic orientation detection using OCR text analysis
    - PDF rotation correction (90°, 180°, 270°)
    - Text confidence scoring to determine best orientation
    - Fallback to original if correction fails
    - Comprehensive logging and error handling

Dependencies:
    - pdf2image: For PDF to image conversion
    - pytesseract: For OCR text extraction
    - PyPDF2: For PDF manipulation and rotation
    - Pillow: For image processing
"""

import os
import tempfile
import math
from typing import Optional, Tuple, Dict
import PyPDF2
from pdf2image import convert_from_path
import pytesseract
from pytesseract import Output
import cv2
import numpy as np
from PIL import Image

# Import project modules
from app_logger import app_logger
from config_loader import POPPLER_PATH, TESSERACT_CMD_PATH

# Configure Tesseract path if specified
if TESSERACT_CMD_PATH:
    pytesseract.pytesseract.tesseract_cmd = TESSERACT_CMD_PATH

def detect_text_orientation_osd(image: Image.Image) -> Dict[str, any]:
    """
    Detect text orientation using Tesseract's OSD (Orientation and Script Detection) mode.

    Args:
        image (PIL.Image): Image to analyze

    Returns:
        Dict[str, any]: Dictionary with OSD results:
            - orientation: Current orientation angle (0, 90, 180, 270)
            - rotate: Degrees to rotate to correct orientation
            - script: Detected script/writing system
            - confidence: OSD confidence score
            - success: Whether OSD was successful
    """
    try:
        # Convert PIL Image to OpenCV format for Tesseract OSD
        image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

        # Use Tesseract's OSD mode to detect orientation and script
        osd_results = pytesseract.image_to_osd(image_cv, output_type=Output.DICT)

        app_logger.debug(f"OSD Results: {osd_results}")

        return {
            'orientation': osd_results.get('orientation', 0),
            'rotate': osd_results.get('rotate', 0),
            'script': osd_results.get('script', 'Unknown'),
            'confidence': osd_results.get('orientation_conf', 0),
            'success': True
        }

    except Exception as e:
        app_logger.warning(f"OSD failed, falling back to OCR-based detection: {e}")

        # Fallback to basic OCR confidence check
        try:
            ocr_data = pytesseract.image_to_data(image, output_type=Output.DICT)
            confidences = [int(conf) for conf in ocr_data['conf'] if int(conf) > 0]
            avg_confidence = sum(confidences) / len(confidences) if confidences else 0

            return {
                'orientation': 0,
                'rotate': 0,
                'script': 'Unknown',
                'confidence': avg_confidence,
                'success': False
            }
        except Exception as fallback_e:
            app_logger.error(f"Both OSD and OCR fallback failed: {fallback_e}")
            return {
                'orientation': 0,
                'rotate': 0,
                'script': 'Unknown',
                'confidence': 0,
                'success': False
            }

def detect_pdf_orientation(pdf_path: str, max_pages: int = 1) -> int:
    """
    Detect the correct orientation for a PDF using Tesseract's OSD mode.

    Args:
        pdf_path (str): Path to the PDF file
        max_pages (int): Maximum number of pages to analyze (default: 1)

    Returns:
        int: Rotation angle needed (0, 90, 180, 270) to correct orientation
    """
    try:
        app_logger.info(f"Detecting orientation for PDF using OSD: {os.path.basename(pdf_path)}")

        # Convert PDF pages to images
        images = convert_from_path(
            pdf_path,
            first_page=1,
            last_page=max_pages,
            poppler_path=POPPLER_PATH,
            dpi=200  # Higher DPI for better OSD accuracy
        )

        if not images:
            app_logger.warning(f"Could not convert PDF to images for orientation detection: {pdf_path}")
            return 0

        # Analyze each page and collect OSD results
        osd_results = []

        for i, image in enumerate(images):
            app_logger.debug(f"Running OSD on page {i+1}")

            # Use OSD to detect orientation
            osd_result = detect_text_orientation_osd(image)

            if osd_result['success']:
                osd_results.append(osd_result)
                app_logger.info(f"Page {i+1} OSD: orientation={osd_result['orientation']}°, "
                               f"rotate={osd_result['rotate']}°, "
                               f"script={osd_result['script']}, "
                               f"confidence={osd_result['confidence']:.1f}")
            else:
                app_logger.warning(f"OSD failed for page {i+1}, skipping")

        if not osd_results:
            app_logger.warning(f"No successful OSD results for {os.path.basename(pdf_path)}")
            return 0

        # Find the most common rotation needed
        rotation_votes = {}
        confidence_weights = {}

        for result in osd_results:
            rotate_angle = result['rotate']
            confidence = result['confidence']

            if rotate_angle not in rotation_votes:
                rotation_votes[rotate_angle] = 0
                confidence_weights[rotate_angle] = 0

            rotation_votes[rotate_angle] += 1
            confidence_weights[rotate_angle] += confidence

        # Choose rotation with highest weighted vote
        best_rotation = 0
        best_score = 0

        for rotation, votes in rotation_votes.items():
            avg_confidence = confidence_weights[rotation] / votes
            weighted_score = votes * avg_confidence

            app_logger.info(f"Rotation {rotation}°: {votes} votes, "
                           f"avg confidence={avg_confidence:.1f}, "
                           f"weighted score={weighted_score:.1f}")

            if weighted_score > best_score:
                best_score = weighted_score
                best_rotation = rotation

        if best_rotation != 0:
            app_logger.info(f"Orientation correction recommended for {os.path.basename(pdf_path)}: "
                           f"{best_rotation}° rotation (weighted score: {best_score:.1f})")
        else:
            app_logger.info(f"No orientation correction needed for {os.path.basename(pdf_path)}")

        return best_rotation

    except Exception as e:
        app_logger.error(f"Error detecting PDF orientation: {e}", exc_info=True)
        return 0

def rotate_pdf(input_path: str, output_path: str, rotation: int) -> bool:
    """
    Rotate a PDF file by the specified angle.
    
    Args:
        input_path (str): Path to input PDF
        output_path (str): Path for output PDF
        rotation (int): Rotation angle (90, 180, 270)
        
    Returns:
        bool: True if rotation was successful, False otherwise
    """
    try:
        app_logger.info(f"Rotating PDF {os.path.basename(input_path)} by {rotation}°")
        
        with open(input_path, 'rb') as input_file:
            pdf_reader = PyPDF2.PdfReader(input_file)
            pdf_writer = PyPDF2.PdfWriter()
            
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                
                # Apply rotation - PyPDF2 rotates clockwise, so we apply the rotation directly
                # The OSD 'rotate' value tells us how much to rotate to correct the orientation
                if rotation == 90:
                    page.rotate(90)
                elif rotation == 180:
                    page.rotate(180)
                elif rotation == 270:
                    page.rotate(270)
                elif rotation == -90:  # Handle negative rotations
                    page.rotate(-90)
                elif rotation == -180:
                    page.rotate(-180)
                elif rotation == -270:
                    page.rotate(-270)
                
                pdf_writer.add_page(page)
            
            # Write rotated PDF
            with open(output_path, 'wb') as output_file:
                pdf_writer.write(output_file)
        
        app_logger.info(f"Successfully rotated PDF: {os.path.basename(output_path)}")
        return True
        
    except Exception as e:
        app_logger.error(f"Error rotating PDF: {e}", exc_info=True)
        return False

def correct_pdf_orientation(pdf_path: str) -> Optional[str]:
    """
    Detect and correct PDF orientation if needed.
    
    Args:
        pdf_path (str): Path to the PDF file to check and correct
        
    Returns:
        Optional[str]: Path to corrected PDF file, or None if no correction needed/failed
    """
    try:
        # Detect required rotation
        required_rotation = detect_pdf_orientation(pdf_path)
        
        if required_rotation == 0:
            # No rotation needed
            return None
        
        # Create temporary file for corrected PDF
        temp_fd, temp_path = tempfile.mkstemp(suffix='.pdf', prefix='corrected_')
        os.close(temp_fd)  # Close file descriptor, we'll write to it later
        
        # Rotate the PDF
        if rotate_pdf(pdf_path, temp_path, required_rotation):
            app_logger.info(f"PDF orientation corrected: {os.path.basename(pdf_path)} "
                           f"rotated {required_rotation}°")
            return temp_path
        else:
            # Clean up temp file if rotation failed
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            return None
            
    except Exception as e:
        app_logger.error(f"Error correcting PDF orientation: {e}", exc_info=True)
        return None
