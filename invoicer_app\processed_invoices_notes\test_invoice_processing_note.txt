Invoice: test_invoice.xlsx
Processing Time: 2025-07-14 14:37:38
AI Provider: openai
AI Confidence: 0.95
Extracted Data:
  invoice_id: INV-2024-002
  vendor_name: Excel Test Company
  invoice_date: 2024-01-16
  due_date: None
  subtotal_eur: 1234.56
  vat_amount_eur: 234.56
  vat_rate: 19.0
  total_amount_eur: 1469.12
  business_unit: None
  original_currency: EUR
  original_total_amount: 1469.12
  original_subtotal: 1234.56
  original_vat_amount: 234.56
  is_duplicate: None
  processing_notes_ref: 
  ai_confidence_score: 0.95
  processing_notes: HYBRID PROCESSING: Text method: Extracted by OpenAI model gpt-4.1-mini. Session: b94a09f5. Duration: 2.37s. Cost: $0.000107 | File method: Processed via OpenAI Responses API with file upload. Model: gpt-4.1-mini, Session: cfb53049, Response: resp_6875081dc784819895a1435df1aa38220ff87bbdeddf7293, Status: completed, File ID: file-22X8aDzFc38qYeFDXt4U2M, Tokens: 762, Cost: $0.000107, Duration: 4.08s
  responses_api_session_id: cfb53049
  responses_api_response_id: resp_6875081dc784819895a1435df1aa38220ff87bbdeddf7293
  responses_api_model_used: gpt-4.1-mini
  responses_api_input_tokens: 658
  responses_api_output_tokens: 104
  responses_api_total_tokens: 762
  responses_api_estimated_cost_usd: 0.00010740000000000001
  responses_api_file_id: file-22X8aDzFc38qYeFDXt4U2M
  responses_api_file_size_bytes: 1718
  comparison_notes: Agreement: 1.00. All fields match between methods.
  field_agreement_score: 1.0
  extraction_methods_used: text_and_file

Notes: HYBRID PROCESSING: Text method: Extracted by OpenAI model gpt-4.1-mini. Session: b94a09f5. Duration: 2.37s. Cost: $0.000107 | File method: Processed via OpenAI Responses API with file upload. Model: gpt-4.1-mini, Session: cfb53049, Response: resp_6875081dc784819895a1435df1aa38220ff87bbdeddf7293, Status: completed, File ID: file-22X8aDzFc38qYeFDXt4U2M, Tokens: 762, Cost: $0.000107, Duration: 4.08s
