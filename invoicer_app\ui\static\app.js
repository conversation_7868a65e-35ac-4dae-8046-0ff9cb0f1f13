let pdfDoc = null;
let pageNum = 1;
let pageRendering = false;
let pageNumPending = null;
let scale = 1.2;
let canvas = null;
let ctx = null;

// PDF Functions
function initializePDFCanvas() {
    canvas = document.getElementById('pdf-canvas');
    if (canvas) {
        ctx = canvas.getContext('2d');
        return true;
    }
    return false;
}

function loadPDF() {
    if (!initializePDFCanvas()) {
        console.error('PDF canvas not found');
        return;
    }
    
    if (typeof pdfjsLib === 'undefined') {
        console.error('PDF.js not loaded');
        return;
    }
    
    const url = `/pdf/${invoiceId}`;
    
    pdfjsLib.getDocument(url).promise.then(function(pdfDoc_) {
        pdfDoc = pdfDoc_;
        document.getElementById('page-info').textContent = `Page ${pageNum} of ${pdfDoc.numPages}`;
        renderPage(pageNum);
    }).catch(function(error) {
        console.error('Error loading PDF:', error);
        if (canvas) {
            canvas.style.display = 'none';
        }
        const pdfSection = document.querySelector('.pdf-section');
        if (pdfSection) {
            pdfSection.innerHTML = '<p>PDF could not be loaded. Error: ' + error.message + '</p>';
        }
    });
}

function renderPage(num) {
    if (!pdfDoc || !ctx) return;
    
    pageRendering = true;
    pdfDoc.getPage(num).then(function(page) {
        const viewport = page.getViewport({scale: scale});
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {
            canvasContext: ctx,
            viewport: viewport
        };
        const renderTask = page.render(renderContext);

        renderTask.promise.then(function() {
            pageRendering = false;
            if (pageNumPending !== null) {
                renderPage(pageNumPending);
                pageNumPending = null;
            }
        });
    });

    document.getElementById('page-info').textContent = `Page ${num} of ${pdfDoc.numPages}`;
}

function queueRenderPage(num) {
    if (pageRendering) {
        pageNumPending = num;
    } else {
        renderPage(num);
    }
}

function prevPage() {
    if (pageNum <= 1) return;
    pageNum--;
    queueRenderPage(pageNum);
}

function nextPage() {
    if (pageNum >= pdfDoc.numPages) return;
    pageNum++;
    queueRenderPage(pageNum);
}

function zoomIn() {
    scale += 0.2;
    queueRenderPage(pageNum);
}

function zoomOut() {
    if (scale <= 0.4) return;
    scale -= 0.2;
    queueRenderPage(pageNum);
}

// Data Functions
function loadInvoiceData() {
    fetch(`/api/invoice/${invoiceId}`)
        .then(response => response.json())
        .then(data => {
            // Populate form fields
            Object.keys(data).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'date') {
                        // Handle date conversion safely
                        const dateValue = data[key];
                        if (dateValue && typeof dateValue === 'string' && dateValue.trim() !== '') {
                            if (!dateValue.includes('-')) {
                                // Assume it's in DD/MM/YYYY format, convert to YYYY-MM-DD
                                const parts = dateValue.split('/');
                                if (parts.length === 3 && 
                                    !isNaN(parts[0]) && !isNaN(parts[1]) && !isNaN(parts[2]) &&
                                    parts[2].length === 4) {
                                    const day = parts[0].padStart(2, '0');
                                    const month = parts[1].padStart(2, '0');
                                    const year = parts[2];
                                    element.value = `${year}-${month}-${day}`;
                                } else {
                                    console.warn(`Invalid date format for ${key}: ${dateValue}`);
                                    element.value = '';
                                }
                            } else {
                                // Already in YYYY-MM-DD format or similar
                                element.value = dateValue;
                            }
                        } else {
                            element.value = '';
                        }
                    } else if (element.type === 'checkbox') {
                        element.checked = data[key] === true || data[key] === 'true' || data[key] === 1 || data[key] === '1';
                    } else {
                        element.value = data[key] || '';
                    }
                }
            });
            // After populating, update the visual status for is_duplicate
            if (document.getElementById('is_duplicate')) {
                updateDuplicateStatus();
            }
        })
        .catch(error => console.error('Error loading invoice data:', error));
}

function getFormData() {
    const form = document.getElementById('invoice-form');
    const formData = new FormData(form);
    const data = {};
    for (let [key, value] of formData.entries()) {
        // For checkboxes, FormData returns 'on' or nothing. We want true/false.
        const checkbox = form.elements[key];
        if (checkbox && checkbox.type === 'checkbox') {
            data[key] = checkbox.checked;
        } else {
            data[key] = value;
        }
    }
    return data;
}

function fetchNextPendingInvoiceIdAndRedirect(actionVerb) {
    fetch('/api/next-pending-invoice-id')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.next_invoice_id) {
                alert(`Invoice ${actionVerb}! Loading next invoice...`);
                window.location.href = '/review/' + data.next_invoice_id;
            } else {
                alert(`Invoice ${actionVerb}! No more invoices pending review. Returning to dashboard.`);
                window.location.href = '/';
            }
        })
        .catch(error => {
            console.error('Error fetching next pending invoice:', error);
            alert(`Invoice ${actionVerb}, but could not fetch next invoice. Returning to dashboard.`);
            window.location.href = '/';
        });
}

function saveChanges() {
    const data = getFormData();
    fetch(`/api/update/${invoiceId}`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            fetchNextPendingInvoiceIdAndRedirect('saved');
        } else {
            alert('Error saving changes: ' + (result.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error saving changes:', error);
        alert('Error saving changes. See console for details.');
    });
}

function approveInvoice() {
    fetch(`/api/approve/${invoiceId}`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'}
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            fetchNextPendingInvoiceIdAndRedirect('approved');
        } else {
            alert('Error approving invoice: ' + (result.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error approving invoice:', error);
        alert('Error approving invoice. See console for details.');
    });
}

function showRejectModal() {
    document.getElementById('reject-modal').style.display = 'block';
}

function closeRejectModal() {
    document.getElementById('reject-modal').style.display = 'none';
}

function confirmReject() {
    const notes = document.getElementById('reject-notes').value;
    fetch(`/api/reject/${invoiceId}`, {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({notes: notes})
    })
    .then(response => response.json())
    .then(result => {
        closeRejectModal();
        if (result.success) {
            fetchNextPendingInvoiceIdAndRedirect('rejected');
        } else {
            alert('Error rejecting invoice: ' + (result.error || 'Unknown error'));
        }
    })
    .catch(error => {
        closeRejectModal();
        console.error('Error rejecting invoice:', error);
        alert('Error rejecting invoice. See console for details.');
    });
}

function updateDuplicateStatus() {
    const checkbox = document.getElementById('is_duplicate');
    const statusSpan = document.getElementById('is_duplicate_status');
    if (checkbox && statusSpan) {
        if (checkbox.checked) {
            statusSpan.textContent = 'DUPLICATE';
            statusSpan.style.color = 'red';
            statusSpan.style.fontWeight = 'bold';
        } else {
            statusSpan.textContent = 'Not a duplicate';
            statusSpan.style.color = 'green'; // Or default color
            statusSpan.style.fontWeight = 'normal';
        }
    }
}

// Invoice Navigation
function navigateToPreviousInvoice() {
    fetch(`/api/invoice/${invoiceId}/previous`)
        .then(response => response.json())
        .then(data => {
            if (data.previous_id) {
                window.location.href = `/review/${data.previous_id}`;
            } else {
                alert('This is the first invoice.');
            }
        })
        .catch(error => console.error('Error navigating to previous invoice:', error));
}

function navigateToNextInvoice() {
    fetch(`/api/invoice/${invoiceId}/next`)
        .then(response => response.json())
        .then(data => {
            if (data.next_id) {
                window.location.href = `/review/${data.next_id}`;
            } else {
                alert('This is the last invoice.');
            }
        })
        .catch(error => console.error('Error navigating to next invoice:', error));
} 