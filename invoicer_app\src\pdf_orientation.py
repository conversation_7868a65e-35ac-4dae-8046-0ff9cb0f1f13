"""
PDF Orientation Detection and Correction Module for MacInvoicer

This module provides functionality to detect and correct PDF orientation issues
during the import/upload stage. It uses OCR-based text analysis to determine
if a PDF is rotated and automatically corrects the orientation for optimal
readability in the viewer.

Key Features:
    - Automatic orientation detection using OCR text analysis
    - PDF rotation correction (90°, 180°, 270°)
    - Text confidence scoring to determine best orientation
    - Fallback to original if correction fails
    - Comprehensive logging and error handling

Dependencies:
    - pdf2image: For PDF to image conversion
    - pytesseract: For OCR text extraction
    - PyPDF2: For PDF manipulation and rotation
    - Pillow: For image processing
"""

import os
import tempfile
import math
from typing import Optional, Tuple, Dict
import PyPDF2
from pdf2image import convert_from_path
import pytesseract
from PIL import Image

# Import project modules
from app_logger import app_logger
from config_loader import POPPLER_PATH, TESSERACT_CMD_PATH

# Configure Tesseract path if specified
if TESSERACT_CMD_PATH:
    pytesseract.pytesseract.tesseract_cmd = TESSERACT_CMD_PATH

def analyze_text_orientation(image: Image.Image) -> Dict[str, float]:
    """
    Analyze text orientation in an image using OCR confidence scores.
    
    Args:
        image (PIL.Image): Image to analyze
        
    Returns:
        Dict[str, float]: Dictionary with orientation analysis results:
            - confidence: OCR confidence score (0-100)
            - text_length: Length of extracted text
            - word_count: Number of words detected
            - line_count: Number of text lines detected
    """
    try:
        # Get detailed OCR data
        ocr_data = pytesseract.image_to_data(image, output_type=pytesseract.Output.DICT)
        
        # Calculate metrics
        confidences = [int(conf) for conf in ocr_data['conf'] if int(conf) > 0]
        text_parts = [text.strip() for text in ocr_data['text'] if text.strip()]
        
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0
        text_length = sum(len(text) for text in text_parts)
        word_count = len(text_parts)
        
        # Count lines by grouping words with similar y-coordinates
        line_count = 0
        if ocr_data['top']:
            y_positions = [y for y, conf in zip(ocr_data['top'], ocr_data['conf']) if int(conf) > 0]
            if y_positions:
                y_positions.sort()
                line_count = 1
                for i in range(1, len(y_positions)):
                    if abs(y_positions[i] - y_positions[i-1]) > 20:  # New line threshold
                        line_count += 1
        
        return {
            'confidence': avg_confidence,
            'text_length': text_length,
            'word_count': word_count,
            'line_count': line_count
        }
        
    except Exception as e:
        app_logger.warning(f"Error analyzing text orientation: {e}")
        return {
            'confidence': 0,
            'text_length': 0,
            'word_count': 0,
            'line_count': 0
        }

def calculate_orientation_score(analysis: Dict[str, float]) -> float:
    """
    Calculate a composite score for text orientation quality.
    
    Args:
        analysis (Dict[str, float]): Analysis results from analyze_text_orientation
        
    Returns:
        float: Composite score (higher is better)
    """
    confidence = analysis['confidence']
    text_length = analysis['text_length']
    word_count = analysis['word_count']
    line_count = analysis['line_count']
    
    # Weighted scoring formula
    # Confidence is most important, followed by text length and structure
    score = (
        confidence * 0.5 +  # OCR confidence (0-100)
        min(text_length / 10, 50) * 0.3 +  # Text length (capped at 500 chars = 50 points)
        min(word_count * 2, 30) * 0.15 +  # Word count (capped at 15 words = 30 points)
        min(line_count * 5, 20) * 0.05  # Line count (capped at 4 lines = 20 points)
    )
    
    return score

def detect_pdf_orientation(pdf_path: str, max_pages: int = 2) -> int:
    """
    Detect the correct orientation for a PDF by testing different rotations.
    
    Args:
        pdf_path (str): Path to the PDF file
        max_pages (int): Maximum number of pages to analyze (default: 2)
        
    Returns:
        int: Rotation angle needed (0, 90, 180, 270) to correct orientation
    """
    try:
        app_logger.info(f"Detecting orientation for PDF: {os.path.basename(pdf_path)}")
        
        # Convert PDF pages to images
        images = convert_from_path(
            pdf_path, 
            first_page=1, 
            last_page=max_pages,
            poppler_path=POPPLER_PATH
        )
        
        if not images:
            app_logger.warning(f"Could not convert PDF to images for orientation detection: {pdf_path}")
            return 0
        
        # Test each rotation angle
        rotation_scores = {}
        
        for rotation in [0, 90, 180, 270]:
            total_score = 0
            page_count = 0
            
            for i, image in enumerate(images):
                # Rotate image for testing
                if rotation != 0:
                    test_image = image.rotate(-rotation, expand=True)  # Negative for correct direction
                else:
                    test_image = image
                
                # Analyze text orientation
                analysis = analyze_text_orientation(test_image)
                score = calculate_orientation_score(analysis)
                total_score += score
                page_count += 1
                
                app_logger.debug(f"Page {i+1}, Rotation {rotation}°: Score={score:.2f}, "
                               f"Confidence={analysis['confidence']:.1f}, "
                               f"Words={analysis['word_count']}, "
                               f"Lines={analysis['line_count']}")
            
            avg_score = total_score / page_count if page_count > 0 else 0
            rotation_scores[rotation] = avg_score
            
            app_logger.info(f"Rotation {rotation}°: Average score = {avg_score:.2f}")
        
        # Find the best rotation
        best_rotation = max(rotation_scores.keys(), key=lambda k: rotation_scores[k])
        best_score = rotation_scores[best_rotation]
        
        app_logger.info(f"Best orientation for {os.path.basename(pdf_path)}: {best_rotation}° "
                       f"(score: {best_score:.2f})")
        
        # Only apply rotation if there's a significant improvement
        original_score = rotation_scores[0]
        if best_rotation != 0 and best_score > original_score * 1.2:  # 20% improvement threshold
            app_logger.info(f"Orientation correction recommended: {best_rotation}° rotation "
                           f"(improvement: {((best_score/original_score - 1) * 100):.1f}%)")
            return best_rotation
        else:
            app_logger.info(f"No orientation correction needed for {os.path.basename(pdf_path)}")
            return 0
            
    except Exception as e:
        app_logger.error(f"Error detecting PDF orientation: {e}", exc_info=True)
        return 0

def rotate_pdf(input_path: str, output_path: str, rotation: int) -> bool:
    """
    Rotate a PDF file by the specified angle.
    
    Args:
        input_path (str): Path to input PDF
        output_path (str): Path for output PDF
        rotation (int): Rotation angle (90, 180, 270)
        
    Returns:
        bool: True if rotation was successful, False otherwise
    """
    try:
        app_logger.info(f"Rotating PDF {os.path.basename(input_path)} by {rotation}°")
        
        with open(input_path, 'rb') as input_file:
            pdf_reader = PyPDF2.PdfReader(input_file)
            pdf_writer = PyPDF2.PdfWriter()
            
            for page_num in range(len(pdf_reader.pages)):
                page = pdf_reader.pages[page_num]
                
                # Apply rotation
                if rotation == 90:
                    page.rotate(90)
                elif rotation == 180:
                    page.rotate(180)
                elif rotation == 270:
                    page.rotate(270)
                
                pdf_writer.add_page(page)
            
            # Write rotated PDF
            with open(output_path, 'wb') as output_file:
                pdf_writer.write(output_file)
        
        app_logger.info(f"Successfully rotated PDF: {os.path.basename(output_path)}")
        return True
        
    except Exception as e:
        app_logger.error(f"Error rotating PDF: {e}", exc_info=True)
        return False

def correct_pdf_orientation(pdf_path: str) -> Optional[str]:
    """
    Detect and correct PDF orientation if needed.
    
    Args:
        pdf_path (str): Path to the PDF file to check and correct
        
    Returns:
        Optional[str]: Path to corrected PDF file, or None if no correction needed/failed
    """
    try:
        # Detect required rotation
        required_rotation = detect_pdf_orientation(pdf_path)
        
        if required_rotation == 0:
            # No rotation needed
            return None
        
        # Create temporary file for corrected PDF
        temp_fd, temp_path = tempfile.mkstemp(suffix='.pdf', prefix='corrected_')
        os.close(temp_fd)  # Close file descriptor, we'll write to it later
        
        # Rotate the PDF
        if rotate_pdf(pdf_path, temp_path, required_rotation):
            app_logger.info(f"PDF orientation corrected: {os.path.basename(pdf_path)} "
                           f"rotated {required_rotation}°")
            return temp_path
        else:
            # Clean up temp file if rotation failed
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            return None
            
    except Exception as e:
        app_logger.error(f"Error correcting PDF orientation: {e}", exc_info=True)
        return None
